# Cloudflare Worker 单ProxyIP稳定性优化说明

## 问题分析

### 1. IP跳来跳去的原因
- **原始问题**: 代码中使用 `Math.floor(Math.random() * proxyIPs.length)` 随机选择ProxyIP
- **影响**: 每次请求都可能使用不同的ProxyIP，导致mihomo-party客户端IP频繁变化

### 2. ProxyIP失效问题
- **原始问题**: ProxyIP失效时没有智能处理机制
- **用户反馈**: 用户更倾向于使用单个ProxyIP，配合ADD（优选IP）使用
- **用户习惯**: ProxyIP失效时，用户习惯手动切换节点来重新激活

## 优化方案（单ProxyIP策略）

### 1. 单ProxyIP稳定性管理
```javascript
// 新增变量
let proxyIPStatus = {
    current: '',
    failureCount: 0,
    lastFailureTime: 0,
    isTemporarilyDisabled: false
};
const MAX_FAILURE_COUNT = 3; // 最大连续失败次数
const RECOVERY_INTERVAL = 600000; // 10分钟后重新尝试
const FAILURE_RESET_INTERVAL = 1800000; // 30分钟后重置失败计数
```

**特性**:
- 专注于单个ProxyIP的稳定性
- 避免随机切换，保持IP一致性
- 智能失败检测和恢复机制

### 2. 智能失败处理
```javascript
function 记录ProxyIP失败(proxyIP)
function 记录ProxyIP成功(proxyIP)
```

**特性**:
- 记录连续失败次数
- 达到阈值时临时禁用ProxyIP
- 连接成功时减少失败计数
- 定期重置失败状态

### 3. 优化的重试策略
```javascript
async function retry() {
    // 记录失败并使用直连
    记录ProxyIP失败(proxyIP);
    // 失败后使用直连，让ADD优选IP发挥作用
    tcpSocket = await connectAndWrite(addressRemote, portRemote);
}
```

**特性**:
- ProxyIP失效时自动切换到直连
- 配合ADD优选IP提供备用连接
- 用户可通过切换节点重新激活ProxyIP

### 4. 自动恢复机制
```javascript
// 10分钟后重新尝试失效的ProxyIP
// 30分钟后重置失败计数
```

**特性**:
- 定期重新尝试失效的ProxyIP
- 给ProxyIP重新机会
- 避免永久禁用

## 配置建议

### 环境变量设置
```bash
# 推荐：单个ProxyIP设置
PROXYIP=your-proxyip.example.com:443

# 或者设置多个ADD优选IP
ADD=ip1.example.com:443,ip2.example.com:443,ip3.example.com:443
```

### 参数调优
```javascript
// 可根据实际情况调整的参数
const MAX_FAILURE_COUNT = 3; // 最大连续失败次数
const RECOVERY_INTERVAL = 600000; // 10分钟重试间隔
const FAILURE_RESET_INTERVAL = 1800000; // 30分钟重置间隔
```

## 预期效果

### 1. IP稳定性大幅提升
- **完全消除随机IP跳跃**：始终使用同一个ProxyIP
- **mihomo-party客户端IP稳定**：不再出现频繁切换
- **用户体验一致性**：连接行为可预测

### 2. 智能故障处理
- **自动检测ProxyIP失效**：连续失败时临时禁用
- **优雅降级到直连**：配合ADD优选IP使用
- **用户主动恢复**：切换节点重新激活ProxyIP

### 3. 符合用户习惯
- **单ProxyIP策略**：符合用户"只要一个proxyip"的需求
- **配合ADD使用**：ProxyIP失效时ADD优选IP发挥作用
- **手动控制**：用户可通过切换节点控制ProxyIP状态

## 工作流程

### 正常情况
1. **使用配置的ProxyIP**：始终使用环境变量中设置的ProxyIP
2. **连接成功**：记录成功状态，减少失败计数
3. **保持稳定**：IP地址不会随机变化

### ProxyIP失效时
1. **检测失败**：连续失败3次后临时禁用ProxyIP
2. **切换直连**：自动使用ADD优选IP或直连
3. **用户感知**：mihomo-party显示不同的IP（ADD中的IP）
4. **手动恢复**：用户切换节点重新激活ProxyIP

### 自动恢复
1. **定期重试**：10分钟后重新尝试失效的ProxyIP
2. **重置计数**：30分钟后重置失败计数
3. **智能恢复**：连接成功时立即恢复正常状态

## 监控和调试

### 日志输出
代码会输出以下关键日志：
- ProxyIP状态信息
- 失败次数记录
- 临时禁用/重新启用通知
- 连接成功/失败状态

### 调试信息
```javascript
// 可在控制台查看ProxyIP状态
console.log(获取ProxyIP状态());
```

## 部署说明

1. 替换原有的 `_worker.js` 文件
2. 设置单个ProxyIP在环境变量中：`PROXYIP=your-proxy.com:443`
3. 配置ADD优选IP：`ADD=ip1.com:443,ip2.com:443`
4. 观察日志输出确认优化生效

## 注意事项

1. **推荐单ProxyIP**：避免设置多个ProxyIP，专注稳定性
2. **配合ADD使用**：设置多个ADD优选IP作为备用
3. **监控日志**：关注ProxyIP状态变化
4. **手动恢复**：ProxyIP失效时可通过切换节点恢复

## 与原版本的区别

| 方面 | 原版本 | 优化版本 |
|------|--------|----------|
| ProxyIP选择 | 随机选择 | 固定使用单个 |
| IP稳定性 | 频繁跳跃 | 完全稳定 |
| 失效处理 | 简单重试 | 智能禁用+自动恢复 |
| 用户体验 | 不可预测 | 可控制 |
| 配置复杂度 | 需要多IP | 单IP即可 |
