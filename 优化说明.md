# Cloudflare Worker 代理节点优化说明

## 问题分析

### 1. IP跳来跳去的原因
- **原始问题**: 代码中使用 `Math.floor(Math.random() * proxyIPs.length)` 随机选择ProxyIP
- **影响**: 每次请求都可能使用不同的ProxyIP，导致客户端IP频繁变化

### 2. ProxyIP失效问题
- **原始问题**: 重试机制简单，没有ProxyIP池轮换
- **影响**: 当ProxyIP失效时，没有智能切换到其他可用IP

## 优化方案

### 1. 智能ProxyIP选择策略
```javascript
// 新增变量
let currentProxyIPIndex = 0;
let proxyIPFailureCount = new Map();
let lastProxyIPRotation = 0;
const PROXYIP_ROTATION_INTERVAL = 300000; // 5分钟轮换
const MAX_FAILURE_COUNT = 3; // 最大失败次数
```

**特性**:
- 基于时间的定期轮换（5分钟一次）
- 优先选择失败次数少的ProxyIP
- 避免频繁随机切换

### 2. ProxyIP健康监控
```javascript
function 记录ProxyIP失败(proxyIP)
function 检查ProxyIP健康状态(proxyIPs)
```

**特性**:
- 记录每个ProxyIP的失败次数
- 自动过滤不健康的ProxyIP
- 连接成功时减少失败计数

### 3. 智能重试机制
```javascript
async function retry() {
    // 记录当前ProxyIP失败
    记录ProxyIP失败(proxyIP);
    
    // 智能选择下一个可用ProxyIP
    retryProxyIP = await 智能选择ProxyIP(proxyIPs);
}
```

**特性**:
- 失败时自动切换到其他ProxyIP
- 避免重复使用失效的ProxyIP
- 支持多ProxyIP轮换

### 4. 自动清理机制
```javascript
// 每小时清理一次失败计数
if (now - global.lastCleanupTime > 3600000) {
    重置ProxyIP失败计数();
}
```

**特性**:
- 定期重置失败计数
- 给失效的ProxyIP重新机会
- 防止永久标记为不可用

## 配置建议

### 环境变量设置
```bash
# ProxyIP设置（支持多个，用逗号或换行分隔）
PROXYIP=proxyip1.example.com:443,proxyip2.example.com:443,proxyip3.example.com:443

# 或者使用换行分隔
PROXYIP=proxyip1.example.com:443
proxyip2.example.com:443
proxyip3.example.com:443
```

### 参数调优
```javascript
// 可根据实际情况调整的参数
const PROXYIP_ROTATION_INTERVAL = 300000; // 轮换间隔（毫秒）
const MAX_FAILURE_COUNT = 3; // 最大失败次数
```

## 预期效果

### 1. IP稳定性提升
- 同一时间段内使用相同ProxyIP
- 减少mihomo-party客户端的IP跳跃
- 提供更稳定的连接体验

### 2. 可用性提升
- 自动检测和切换失效ProxyIP
- 智能重试机制提高连接成功率
- 多ProxyIP负载均衡

### 3. 性能优化
- 减少不必要的连接重试
- 优先使用健康的ProxyIP
- 自动恢复机制

## 监控和调试

### 日志输出
代码会输出以下关键日志：
- ProxyIP轮换信息
- 失败次数记录
- 健康状态检查结果
- 重试切换过程

### 建议监控指标
- ProxyIP失败率
- 连接成功率
- 轮换频率
- 重试次数

## 部署说明

1. 替换原有的 `_worker.js` 文件
2. 设置多个ProxyIP在环境变量中
3. 观察日志输出确认优化生效
4. 根据实际情况调整参数

## 注意事项

1. 确保ProxyIP列表中至少有2个可用IP
2. 定期检查ProxyIP的有效性
3. 监控日志以及时发现问题
4. 可根据网络环境调整轮换间隔和失败阈值
